.footer {
  background: #2c3e50;
  color: white;
  position: relative;
  z-index: 50;
  //   margin-top: 0;
  // padding-top: 1rem;

  // Animation optionnelle (désactivée pour éviter les problèmes)
  // transform: translateY(100%);
  // transition: transform 0.4s ease-out;
  // &.visible {
  //   transform: translateY(0);
  // }
}

  .footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    // align-items: center;
    
  }

  .footer-col {
    h3 {
      color: #3498db;
      margin-bottom: 1rem;
    }

    a {
      display: block;
      color: #ecf0f1;
      margin-bottom: 0.5rem;
      text-decoration: none;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        color: #28a745;
        text-decoration: underline;
        text-decoration-color: #28a745;
        text-underline-offset: 3px;
      }

      &:visited {
        color: #28a745;
      }
    }
  }

.brand {
  display: flex;
  flex-direction: column; // <= colonne au lieu de ligne
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  // margin-top: 3rem;
}
span{
      color: #3498db;
      // margin-bottom: 1rem;
}


  .social-icons {
    display: flex;
    gap: 1rem;

    a {
      color: white;
      transition: all 0.3s ease;
      text-decoration: none;
      position: relative;

      &:hover {
        transform: translateY(-3px);
        color: #28a745;
        text-decoration: underline;
        text-decoration-color: #28a745;
        text-underline-offset: 3px;
      }

      &:visited {
        color: #28a745;
      }
    }
  }

  .contact-button {
    color: #ecf0f1 !important;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      color: #28a745 !important;
      text-decoration: underline !important;
      text-decoration-color: #28a745 !important;
      text-underline-offset: 3px !important;
    }

    &:visited {
      color: #28a745 !important;
    }
  }

  // Styles pour les liens de réseaux sociaux avec icônes Bootstrap
  .vstack a {
    color: #ecf0f1 !important;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      color: #28a745 !important;
      text-decoration: underline !important;
      text-decoration-color: #28a745 !important;
      text-underline-offset: 3px !important;
      transform: translateY(-2px);
    }

    &:visited {
      color: #28a745 !important;
    }

    i {
      transition: all 0.3s ease;
    }
  }

  .copyright {
    text-align: center;
    margin-top: -1.5rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
  }
