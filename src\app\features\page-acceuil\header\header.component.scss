// Variables
$top-bar-height: 35px;
$main-bar-height: 60px;
$compact-height: 50px;
$transition-duration: 0.3s;

// Top Bar Styles
.top-bar {
  background-color: #000;
  color: #fff;
  display: flex;
  justify-content: space-between;
  padding: 5px 30px;
  font-size: 14px;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: $top-bar-height;
  z-index: 1002;
  transition: all $transition-duration ease-in-out;

  .left {
    display: flex;
    align-items: center;

    .contact-link {
      margin-right: 15px;
      display: flex;
      align-items: center;
      color: #28a745; // Vert par défaut
      text-decoration: none;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        text-decoration: underline;
        text-decoration-color: #28a745;
        text-underline-offset: 3px;
        color: #20c997; // Vert plus clair au hover
      }

      &:visited {
        color: #28a745;
      }

      mat-icon {
        margin-right: 5px;
        font-size: 16px;
        height: 16px;
        width: 16px;
      }
    }

    span {
      margin-right: 15px;
      display: flex;
      align-items: center;

      mat-icon {
        margin-right: 5px;
        font-size: 16px;
        height: 16px;
        width: 16px;
      }

      &.divider {
        margin: 0 10px;
        opacity: 0.5;
      }
    }
  }
  
  .right {
    display: flex;
    align-items: center;

    a {
      margin-left: 15px;
      color: white;
      text-decoration: none;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        color: #28a745;
        text-decoration: underline;
        text-decoration-color: #28a745;
        text-underline-offset: 3px;
      }

      &:visited {
        color: #28a745;
      }
    }

    mat-icon {
      margin-left: 15px;
      cursor: pointer;
      font-size: 16px;
      height: 16px;
      width: 16px;
      transition: all 0.3s ease;
      color: white;

      &:hover {
        color: #28a745;
        text-decoration: underline;
        text-decoration-color: #28a745;
        text-underline-offset: 3px;
      }
    }
  }

  &.hidden {
    transform: translateY(-100%);
    opacity: 0;
  }
}



// Main Bar Styles
.main-bar {
  background-color: white;
  display: flex;
  justify-content: space-between;
  padding: 10px 30px;
  padding-bottom: 10px;
  align-items: center;
  position: fixed;
  top: $top-bar-height;
  left: 0;
  right: 0;
  height: $main-bar-height;
  z-index: 1001;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all $transition-duration ease-in-out;
  
  .logo-container {
    display: flex;
    align-items: center;
    height: 100%;
    flex: 0 0 auto;
    
    img {
      height: 100%;
      margin-right: 10px;
      margin-left: 0px;
    }
    
    .title {
      font-size: 24px;
      font-weight: bold;
      color: #1f3c88;
      transition: font-size $transition-duration;
    }

  }
  
  .center {
    flex-wrap: nowrap;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1 1 auto;
    a.btn {
      margin: 0 6px;
      font-weight: bold;
      color: black;
      text-decoration: none;
      transition: all 0.3s ease;
      position: relative;

      &:hover:not(.active) {
        color: #28a745;
        text-decoration: underline;
        text-decoration-color: #28a745;
        text-underline-offset: 3px;
      }

      &:visited {
        color: #28a745;
      }

      &.active {
        color: #1f3c88;
      }
    }
  }
  
  .right {
    button {
      margin-left: 10px;
      border: none;
      padding: 8px 14px;
      border-radius: 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        opacity: 0.9;
        transform: translateY(-1px);
      }
    }
    
    .login-btn {
      background-color: #92c0f5;
      color: white;
    }
    
    .signup-btn {
      background-color: #7cc56f;
      color: white;
    }
  }

  // Style compact au scroll
  &.compact {
    top: 0;
    height: $compact-height;
    
    .logo-container {
      img {
        height: 80%;
      }
      
      .title {
        font-size: 20px;
      }
    }
    
    .center a.btn {
      font-size: 14px;
    }
    
    .right button {
      padding: 6px 12px;
      font-size: 14px;
    }
  }
}

// ---------------------------------------------------------

/* Ajoutez ces styles à votre fichier SCSS */

/* Centrer les liens pour bénéficiaire */
.beneficiary-links {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Bouton cercle profil */
.profile-circle-btn {
  width: 70%;
  height: 70px;
  border-radius: 50%;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 15px;
  margin-right: 15px;
  
  mat-icon {
    color: #555;
    font-size: 24px;
  }
  
  &:hover {
    background: #e0e0e0;
    transform: scale(1.05);
  }
}

/* Menu dropdown */
.profile-dropdown-menu {
  position: fixed;
  top: 95px; /* Ajustez selon votre hauteur de header */
  right: 30px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 200px;
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  
  a {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    position: relative;

    mat-icon {
      font-size: 20px;
    }

    &:hover {
      background: #f5f5f5;
      color: #28a745;
      text-decoration: underline;
      text-decoration-color: #28a745;
      text-underline-offset: 3px;
    }

    &:visited {
      color: #28a745;
    }
  }
}

/* Animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.dropdownAnimation {
  animation: fadeIn 0.2s ease-out forwards;
}
